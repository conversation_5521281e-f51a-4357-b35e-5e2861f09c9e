export default {
  // Basic UI
  ui: {
    title: 'Knowledge Base Chat',
    newChat: 'New Chat',
    clear: 'Clear',
    cancel: 'Cancel',
    send: 'Send',
    upload: 'Upload',
    preview: 'Preview',
    copy: 'Copy',
    expand: 'Expand',
    collapse: 'Collapse',
    delete: 'Delete',
    remove: 'Remove',
    close: 'Close',
    confirm: 'Confirm',
    rename: 'Rename',
    export: 'Export',
  },

  // Chat Status
  status: {
    connected: 'Connected',
    disconnected: 'Not Connected',
    typing: 'Typing',
    thinking: 'Thinking...',
    idle: 'Idle',
    processing: '🤖 Processing your question...',
    connecting: '🚀 Starting intelligent dialogue query...',
    cancelled: '🚫 Dialogue cancelled',
  },

  // Sidebar
  sidebar: {
    sessions: 'Session List',
    createNewSession: 'New Session',
    deleteSession: 'Delete Session',
    renameSession: 'Rename Session',
    exportSession: 'Export Session',
    noSessions: 'No session records',
  },

  // Chat Area
  chat: {
    welcomeTitle: 'Welcome to Knowledge Base Chat',
    inputPlaceholder: 'Enter your message here...',
    messageInputPlaceholder: 'Please enter your question...',
    selectKnowledgeBase: 'Select Knowledge Base',
    clearConversation: 'Clear Conversation',
    cancelCurrentQuery: 'Cancel Current Query',
    scrollToBottom: 'Scroll to Bottom',
    regenerateResponse: 'Regenerate Response',
    showSources: 'Show Knowledge Snippets',
  },

  // File Upload
  fileUpload: {
    tooltip: {
      formats: 'Recommended formats: jpg, png, jpeg, txt, md, pdf, docx, xlsx, pptx',
      warning: 'Note: ppt, doc, xls are legacy formats that may cause additional overhead',
      sizeLimit: 'Size: ≤20MB, max 5 files',
    },
    status: {
      uploading: 'Uploading',
      converting: 'Converting',
      done: 'Done',
      error: 'Error',
    },
    messages: {
      uploadSuccess: 'File uploaded successfully',
      uploadFailed: 'File upload failed',
      convertSuccess: 'File converted successfully',
      convertFailed: 'File conversion failed',
      sizeExceeded: 'File size exceeds limit',
      countExceeded: 'File count exceeds limit',
      typeNotSupported: 'Unsupported file type',
      noFileSelected: 'Please select a file',
      removeConfirm: 'Are you sure you want to delete this file?',
      previewNotAvailable: 'File path does not exist, cannot preview',
    },
  },

  // Quick Questions
  quickQuestions: {
    q1: 'Display the hidden danger investigation standards for hazardous chemicals in table format?',
    q2: 'What are the classifications of original vouchers?',
    q3: 'Summarize the current document.',
    q4: 'Please explain the related concepts',
  },

  // Source Modal
  sourceModal: {
    title: 'Knowledge Snippets',
    singleSource: 'Snippet',
    multipleSource: 'Snippet',
    sourceTypes: {
      text: 'Text',
      image: 'Image',
      document: 'Document',
      file: 'File',
      unknown: 'Unknown',
    },
    actions: {
      copyContent: 'Copy Content',
      copySuccess: 'Copied Successfully',
      expandView: 'Expand View',
    },
  },

  // File Preview
  filePreview: {
    title: 'File Preview',
    loading: 'Loading...',
    loadFailed: 'Load Failed',
    unsupported: 'This file type is not supported for preview',
    download: 'Download File',
  },

  // Message
  message: {
    user: 'User',
    assistant: 'Assistant',
    system: 'System',
    time: {
      justNow: 'Just now',
      minutesAgo: 'minutes ago',
      hoursAgo: 'hours ago',
      daysAgo: 'days ago',
      monthsAgo: 'months ago',
      yearsAgo: 'years ago',
    },
  },

  // Error Messages
  errors: {
    networkError: 'Network connection error',
    serverError: 'Server error',
    sessionNotFound: 'Session not found',
    knowledgeBaseNotSelected: 'Please select a knowledge base first',
    messageEmpty: 'Message content cannot be empty',
    uploadError: 'File upload failed',
    conversionError: 'File conversion failed',
    sessionCreateFailed: 'Failed to create session',
    sessionDeleteFailed: 'Failed to delete session',
    sessionRenameFailed: 'Failed to rename session',
    sessionExportFailed: 'Failed to export session',
    unexpectedError: 'An unexpected error occurred',
    loadCollectionsFailed: 'Failed to load collections list',
    loadSessionsFailed: 'Failed to load sessions list',
    loadSessionFailed: 'Failed to load session',
    sendMessageFailed: 'Failed to send message',
    startChatFailed: 'Failed to start chat',
    sseConnectionFailed: 'Unable to establish SSE connection, please check if backend service is running normally',
    sseConnectionError: 'SSE connection disconnected, please check network or backend service status',
    cancelSessionFailed: 'Failed to cancel session',
    exportDataEmpty: 'Export failed: Server returned empty data',
  },

  // Success Messages
  success: {
    sessionCreated: 'Session created successfully',
    sessionDeleted: 'Session deleted successfully',
    sessionRenamed: 'Session renamed successfully',
    sessionExported: 'Session exported successfully',
    messageCleared: 'Conversation cleared',
    fileCopied: 'Content copied to clipboard',
    connectionEstablished: 'Intelligent dialogue connection established, you can start chatting',
    sessionCancelled: 'Current dialogue cancelled',
    exportSuccess: 'Export successful',
  },

  // Confirmation Dialogs
  confirm: {
    deleteSession: 'Are you sure you want to delete this session? This action cannot be undone.',
    clearMessages: 'Are you sure you want to clear the current conversation? This action cannot be undone.',
    cancelQuery: 'Are you sure you want to cancel the current query?',
  },

  // Tooltips
  tooltip: {
    toggleSidebar: 'Toggle Sidebar',
    newChat: 'New Chat',
    clearChat: 'Clear Chat',
    cancelQuery: 'Cancel Current Query',
    scrollToBottom: 'Scroll to Bottom',
    uploadFile: 'Upload File',
    sendMessage: 'Send Message',
    copyContent: 'Copy Content',
    previewFile: 'Preview File',
    removeFile: 'Remove File',
    expandSource: 'Expand View',
    collapseSource: 'Collapse',
  },

  // Session related
  session: {
    unnamed: 'Unnamed Session',
    newConversation: 'New Conversation',
    exportFileName: 'Chat Record',
    loadingHistory: 'Loading session history',
    loadingCollections: 'Loading collections list',
    loadingSessions: 'Loading sessions list',
    deletingSession: 'Deleting session',
    renamingSession: 'Renaming session',
    exportingSession: 'Exporting session',
    endingSession: 'Ending current session',
  },

  // SSE message types
  sse: {
    retrievedSources: '📚 Retrieved {0} relevant sources',
    dialogComplete: '✅ Dialogue complete',
    dialogCancelled: '🚫 Dialogue cancelled',
  },

  // Log messages
  log: {
    sessionLoaded: 'Successfully loaded sessions list, count:',
    sessionHistoryLoaded: 'Successfully loaded session history, message count:',
    sessionDeleted: 'Session deleted successfully:',
    sessionRenamed: 'Session renamed successfully:',
    sessionExported: 'Session exported successfully, filename:',
    sessionStateUpdated: 'Session state updated (auto-saved by backend):',
    currentSessionStateUpdated: 'Current session state updated:',
    messagesCleared: 'Messages list cleared',
    componentUnmounted: 'useKnowledgeGraphChat component unmounted and resources cleaned',
  },
};