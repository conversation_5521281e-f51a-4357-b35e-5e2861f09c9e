import { ref, reactive, computed, nextTick, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user';
import { useI18n } from '/@/hooks/web/useI18n';
import { 
  startKnowledgeGraphChatStream, 
  createSSEConnection, 
  cancelRagSession,
  getCollections,
  getUserChatSessions,
  getChatSessionHistory,
  deleteChatSession,
  updateSessionTitle,
  exportChatSession,
  endChatSession,
  type ChatSessionInfo,
  type ChatMessage as APIChatMessage
} from '/@/api/rag/index';
import type { 
  ChatMessage, 
  SSEMessage, 
  ChatSession, 
  ChatState, 
  KnowledgeGraphChatRequest,
  SourceInfo,
  ImageInfo,
  FileInfo
} from '../types';
import { useMarkdown } from './useMarkdown';
import { useFileUpload } from './useFileUpload';

export function useKnowledgeGraphChat() {
  const userStore = useUserStore();
  const { renderMarkdown, formatTime, formatDate } = useMarkdown();
  const { t } = useI18n('knowledgeGraphChat');
  
  // 文件上传功能
  const fileUpload = useFileUpload();
  
  // 状态管理
  const state = reactive<ChatState>({
    isConnected: false,
    isTyping: false,
    currentSessionId: undefined,
    userId: String(userStore.getUserInfo?.username || 'anonymous'),
    collectionName: 'yq_1916111815235076097' // 暂时先将知识库设置为"训练库"
  });
  
  // 消息列表
  const messages = ref<ChatMessage[]>([]);
  
  // 当前输入的消息
  const inputMessage = ref('');
  
  // 集合列表
  const collections = ref<Array<{ name: string; value: string; description?: string; count?: number }>>([]);
  
  // 会话列表
  const sessions = ref<ChatSession[]>([]);
  
  // 分页相关状态
  const pagination = reactive({
    pageNo: 1,
    pageSize: 20,
    total: 0
  });
  
  // 当前会话
  const currentSession = ref<ChatSession | null>(null);
  
  // SSE连接
  let eventSource: EventSource | null = null;
  
  // DOM引用
  const messagesContainer = ref<HTMLElement>();
  const messageInput = ref<HTMLTextAreaElement>();
  
  // 计算属性
  const canSend = computed(() => {
    return inputMessage.value.trim() && !state.isTyping && !fileUpload.uploadState.isUploading;
  });
  
  const statusText = computed(() => {
    if (state.isTyping) {
      return t('status.thinking');
    }
    if (state.isConnected) {
      return t('status.connected');
    }
    return t('status.disconnected');
  });
  
  // 滚动到底部按钮的显示状态
  const showScrollToBottomButton = ref(false);
  
  // 初始化
  const initialize = async () => {
    await loadCollections();
    await loadSessions();
    
    // 页面加载时立即建立SSE连接
    if (!state.currentSessionId) {
      // 生成新的会话ID
      state.currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 建立SSE连接
    await establishSSEConnection(state.currentSessionId);
  };
  
  // 加载集合列表
  const loadCollections = async () => {
    try {
      // 使用用户ID作为identifier，如果没有则使用默认值
      const identifier = userStore.getUserInfo?.username || 'admin';
      const response = await getCollections(identifier);
      
      if (response.code === 200) {
        // 将message对象转换为数组格式
        const collectionsMap = response.data || {};
        collections.value = Object.entries(collectionsMap).map(([displayName, actualName]) => ({
          name: displayName,        // 显示名称用于界面显示
          value: actualName,        // 实际集合名称用于API调用
          description: displayName,
          count: undefined
        }));
        // console.log('成功加载集合列表，数量:', collections.value.length);
      } else {
        console.warn('获取集合列表失败，错误代码:', response.code);
        collections.value = [];
      }
    } catch (error) {
      console.error('加载集合列表失败:', error);
      message.error(t('errors.loadCollectionsFailed'));
      collections.value = [];
    }
  };
  
  // 加载会话列表
  const loadSessions = async (pageNo?: number, pageSize?: number) => {
    try {
      // 如果传入了分页参数，更新状态
      if (pageNo !== undefined) {
        pagination.pageNo = pageNo;
      }
      if (pageSize !== undefined) {
        pagination.pageSize = pageSize;
      }
      
      // console.log('正在加载会话列表，用户ID:', state.userId);
      const response = await getUserChatSessions(state.userId, pagination.pageSize, (pagination.pageNo - 1) * pagination.pageSize);
      
      if (response.code === 200) {
        // 将API返回的ChatSessionInfo转换为前端ChatSession格式
        const responseData = response.data as any;
        
        // 检查新的分页结构
        if (responseData && typeof responseData === 'object' && responseData.records && Array.isArray(responseData.records)) {
          // 新的分页结构
          sessions.value = responseData.records.map((apiSession: any) => {
            const converted = convertApiSessionToLocal(apiSession);
            return converted;
          });
          
          // 更新分页信息
          pagination.total = responseData.total || 0;
          // 也可以验证其他分页字段
          // pagination.pageNo = responseData.page || pagination.pageNo;
          // pagination.pageSize = responseData.page_size || pagination.pageSize;
        } else if (Array.isArray(responseData)) {
          // 兼容旧的直接数组格式
          sessions.value = responseData.map((apiSession: any) => {
            const converted = convertApiSessionToLocal(apiSession);
            return converted;
          });
          
          // 如果没有分页信息，设置总数为当前数据长度
          pagination.total = responseData.length;
        } else {
          sessions.value = [];
          pagination.total = 0;
        }
        
        console.log(`会话列表加载成功: 当前页=${pagination.pageNo}, 每页=${pagination.pageSize}, 总数=${pagination.total}, 本页数量=${sessions.value.length}`);
        
      } else {
        console.warn('获取会话列表失败:', response.message);
        sessions.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error('加载会话列表失败:', error);
      // 如果是网络错误或服务器错误，不显示错误信息，避免干扰用户
      sessions.value = [];
      pagination.total = 0;
    }
  };
  
  // 处理分页变化
  const handlePageChange = (pageNo: number, pageSize: number) => {
    loadSessions(pageNo, pageSize);
  };
  
  // 安全地转换时间戳
  const safeTimestamp = (value: any): number => {
    if (!value) return Date.now();
    
    // 如果已经是数字，直接返回
    if (typeof value === 'number') {
      return isNaN(value) ? Date.now() : value;
    }
    
    // 尝试解析字符串
    if (typeof value === 'string') {
      const timestamp = new Date(value).getTime();
      return isNaN(timestamp) ? Date.now() : timestamp;
    }
    
    return Date.now();
  };

  // 将API返回的会话数据转换为前端格式
  const convertApiSessionToLocal = (apiSession: any): ChatSession => {
    return {
      id: apiSession.session_id || apiSession.id,
      name: apiSession.session_title || apiSession.title || t('session.unnamed'),
      created_at: safeTimestamp(apiSession.create_time || apiSession.created_at),
      last_activity: safeTimestamp(apiSession.last_message_time || apiSession.last_activity),
      message_count: apiSession.total_messages || apiSession.message_count || 0
    };
  };
  
  // 将API返回的ChatMessage转换为前端ChatMessage格式
  const convertApiMessageToLocal = (apiMessage: APIChatMessage): ChatMessage => {
    const localMessage: ChatMessage = {
      id: apiMessage.id,
      type: apiMessage.role as 'user' | 'assistant',
      content: apiMessage.content,
      timestamp: safeTimestamp(apiMessage.created_at),
      sources: [],
      images: [],
      files: []
    };
    
    // 处理来源信息
    if (apiMessage.source_info && apiMessage.source_info.sources) {
      localMessage.sources = apiMessage.source_info.sources.map(source => ({
        type: source.type as 'text' | 'image' | 'entity' | 'chunk',
        content: source.content,
        url: source.url,
        name: source.name
      }));
      
      // 分离图片信息
      localMessage.images = apiMessage.source_info.sources
        .filter(source => source.type === 'image' && source.url)
        .map(source => ({
          url: source.url!,
          name: source.name,
          alt: source.name
        }));
    }
    
    // 处理metadata中的用户上传文件信息
    if (apiMessage.metadata) {
      const allImages: ImageInfo[] = [...(localMessage.images || [])];
      const allFiles: FileInfo[] = [];
      
      // 处理用户上传的文件，根据后端返回格式分离图片和文件
      if (apiMessage.metadata.user_files && Array.isArray(apiMessage.metadata.user_files)) {
        apiMessage.metadata.user_files
          .filter((file: any) => file && file.url && file.name)
          .forEach((file: any) => {
            // 优先使用后端返回的type字段，如果没有则从文件名提取
            let ext = '';
            if (file.type) {
              // 后端type格式：".jpg" → "jpg"
              ext = file.type.startsWith('.') ? file.type.slice(1).toLowerCase() : file.type.toLowerCase();
            } else {
              // 从文件名提取扩展名作为备选
              ext = file.name.toLowerCase().split('.').pop() || '';
            }
            
            // 判断是否为图片文件
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
              // 检查图片是否已经存在（通过URL去重）
              const existingImage = allImages.find(img => img.url === file.url);
              if (!existingImage) {
                allImages.push({
                  url: file.url,
                  name: file.name,
                  alt: file.name
                });
              }
            } else {
              // 非图片文件保存到用户文件列表
              allFiles.push({
                url: file.url,
                name: file.name,
                type: file.type || ext || 'unknown'
              });
            }
          });
      }
      
      // 设置最终的图片和文件数组
      if (allImages.length > 0) {
        localMessage.images = allImages;
      }
      
      if (allFiles.length > 0) {
        localMessage.files = allFiles;
      }
    }
    
    return localMessage;
  };
  
  // 保存会话到本地（已废弃，后端会自动保存）
  const saveSession = (session: ChatSession) => {
    // 不再使用localStorage，后端会自动保存会话
    // 只更新本地状态
    const existingIndex = sessions.value.findIndex(s => s.id === session.id);
    if (existingIndex >= 0) {
      sessions.value[existingIndex] = session;
    } else {
      sessions.value.unshift(session);
    }
    console.log(t('log.sessionStateUpdated'), session.id);
  };
  
  // 创建新会话
  const createNewSession = async () => {
    // 结束之前的会话
    await endCurrentSession();
    
    // 清空消息
    messages.value = [];
    currentSession.value = null;
    
    // 生成新的会话ID
    state.currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 建立新的SSE连接
    await establishSSEConnection(state.currentSessionId);
    
    // console.log('创建新会话:', state.currentSessionId);
  };
  
  // 切换会话
  const switchSession = async (sessionId: string) => {
    if (state.currentSessionId === sessionId) {
      return;
    }
    
    // console.log('切换会话:', sessionId);
    
    // 结束之前的会话
    await endCurrentSession();
    
    // 加载目标会话
    await loadSession(sessionId);
  };
  
  // 加载会话
  const loadSession = async (sessionId: string) => {
    try {
      // console.log('正在加载会话历史:', sessionId);
      
      // 从后端API获取会话历史
      const response = await getChatSessionHistory(sessionId, 100, 0);
      
      if (response.code === 200) {
        // 将API返回的消息转换为前端格式
        const messageData = response.data || [];
        messages.value = messageData.map(apiMessage => convertApiMessageToLocal(apiMessage));
        // console.log('成功加载会话历史，消息数量:', messages.value.length);
      } else {
        console.warn('获取会话历史失败:', response.message);
        messages.value = [];
      }
      
      // 更新当前会话状态
      state.currentSessionId = sessionId;
      currentSession.value = sessions.value.find(s => s.id === sessionId) || null;
      
      // 建立SSE连接
      await establishSSEConnection(sessionId);
      
      // 加载历史会话时不自动滚动到底部，让用户从对话开头阅读
      // nextTick(() => {
      //   scrollToBottom();
      // });
      
    } catch (error) {
      console.error('加载会话失败:', error);
      message.error(t('errors.loadSessionFailed'));

      // 出错时仍然设置会话ID并建立连接
      state.currentSessionId = sessionId;
      currentSession.value = sessions.value.find(s => s.id === sessionId) || null;
      messages.value = [];

      // 建立SSE连接
      await establishSSEConnection(sessionId);
    }
  };
  
  // 保存当前会话（已废弃，后端会自动保存）
  const saveCurrentSession = () => {
    if (!state.currentSessionId) return;
    
    // 后端会自动保存会话和消息，这里只需要更新本地状态
    const session: ChatSession = {
      id: state.currentSessionId,
      name: getSessionName(),
      created_at: currentSession.value?.created_at || Date.now(),
      last_activity: Date.now(),
      message_count: messages.value.filter(m => m.type === 'user').length
    };
    
    saveSession(session);
    currentSession.value = session;
    
    // console.log('当前会话状态已更新:', session.id);
  };
  
  // 获取会话名称
  const getSessionName = (): string => {
    const firstUserMessage = messages.value.find(m => m.type === 'user');
    if (firstUserMessage) {
      return firstUserMessage.content.substring(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '');
    }
    return t('session.newConversation');
  };
  
  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      // console.log('正在删除会话:', sessionId);
      
      // 调用后端API删除会话
      const response = await deleteChatSession(sessionId);
      
      if (response.code === 200) {
        // 从本地状态中移除会话
        sessions.value = sessions.value.filter(s => s.id !== sessionId);
        // console.log('会话删除成功:', sessionId);
        
        // 如果删除的是当前会话，创建新会话
        if (state.currentSessionId === sessionId) {
          await createNewSession();
        }
      } else {
        console.error('删除会话失败:', response.message);
        message.error(t('errors.sessionDeleteFailed') + ': ' + response.message);
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      message.error(t('errors.sessionDeleteFailed'));
    }
  };
  
  // 重命名会话
  const renameSession = async (sessionId: string, newName: string) => {
    try {
      // console.log('正在重命名会话:', sessionId, '新名称:', newName);
      
      // 调用后端API更新会话标题
      const response = await updateSessionTitle(sessionId, newName);
      
      if (response.code === 200) {
        // 更新本地状态
        const session = sessions.value.find(s => s.id === sessionId);
        if (session) {
          session.name = newName;
        }
        
        if (currentSession.value && currentSession.value.id === sessionId) {
          currentSession.value.name = newName;
        }
        
        console.log(t('log.sessionRenamed'), sessionId);
      } else {
        console.error('重命名会话失败:', response.message);
        message.error(t('errors.sessionRenameFailed') + ': ' + response.message);
      }
    } catch (error) {
      console.error('重命名会话失败:', error);
      message.error(t('errors.sessionRenameFailed'));
    }
  };
  
  // 导出会话
  const exportSession = async (sessionId: string) => {
    try {
      // console.log('正在导出会话:', sessionId);
      
      // 调用后端API导出会话
      const response = await exportChatSession(sessionId);
      // console.log('导出会话响应:', response);
      
      if (response.code === 200) {
        const exportData = response.data as any;
        
        // 检查导出数据是否有效
        if (!exportData) {
          console.error('导出数据为空');
          message.error(t('errors.exportDataEmpty'));
          return;
        }

        // 生成文件名，提供安全的默认值
        let fileName = t('session.exportFileName');
        
        try {
          // 优先从后端返回的session.title获取标题
          let sessionTitle: string | null | undefined = null;
          
          if (exportData.session?.title) {
            sessionTitle = exportData.session.title;
          } else if (exportData.session?.session_title) {
            sessionTitle = exportData.session.session_title;
          } else {
            // 从会话列表中查找标题作为后备
            const session = sessions.value.find(s => s.id === sessionId);
            sessionTitle = session?.name;
          }
          
          if (sessionTitle && sessionTitle.trim()) {
            // 清理文件名中的特殊字符
            const cleanTitle = sessionTitle.replace(/[<>:"/\\|?*]/g, '_').substring(0, 50);
            fileName = `${t('session.exportFileName')}_${cleanTitle}`;
          } else {
            fileName = `${t('session.exportFileName')}_${sessionId.substring(0, 8)}`;
          }
        } catch (nameError) {
          console.warn('获取会话标题失败，使用默认文件名:', nameError);
          fileName = `${t('session.exportFileName')}_${sessionId.substring(0, 8)}`;
        }
        
        // 添加日期后缀
        const dateStr = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_${dateStr}.json`;
        
        // 创建下载链接
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fullFileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        message.success(t('success.exportSuccess'));
        // console.log('会话导出成功，文件名:', fullFileName);
        // console.log('导出数据结构:', {
        //   会话信息: exportData.session,
        //   消息数量: exportData.messages?.length || 0,
        //   导出时间: exportData.exported_at
        // });
        
      } else {
        console.error('导出会话失败:', response.message);
        message.error(t('errors.sessionExportFailed') + ': ' + response.message);
      }
    } catch (error: any) {
      console.error('导出会话失败:', error);

      // 提供更详细的错误信息
      let errorMessage = t('errors.sessionExportFailed');
      if (error?.message) {
        errorMessage += ': ' + error.message;
      }

      message.error(errorMessage);
    }
  };
  
  // 发送消息
  const sendMessage = async () => {
    if (!canSend.value) return;
    
    const question = inputMessage.value.trim();
    inputMessage.value = '';
    
    try {
      // 处理上传的文件信息
      const userImages: ImageInfo[] = [];
      const userFiles: any[] = [];
      
      // 获取已上传的文件
      if (fileUpload.fileList.value.length > 0) {
        fileUpload.fileList.value.forEach(file => {
          if (file.status === 'done' && file.url) {
            // 判断是否为图片文件
            const ext = file.name.toLowerCase().split('.').pop() || '';
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
              userImages.push({
                url: file.url,
                name: file.name,
                alt: file.name
              });
            } else {
              // 非图片文件保存到用户文件列表
              userFiles.push({
                name: file.name,
                url: file.url,
                type: ext
              });
            }
          }
        });
      }
      
      // 添加用户消息
      const userMessage: ChatMessage = {
        id: `user_${Date.now()}`,
        type: 'user',
        content: question,
        timestamp: Date.now(),
        images: userImages.length > 0 ? userImages : undefined,
        files: userFiles.length > 0 ? userFiles : undefined
      };
      
      messages.value.push(userMessage);
      
      // 滚动到底部
      await nextTick();
      scrollToBottom();
      
      // 准备对话历史
      const chatHistory = messages.value
        .filter(m => m.type === 'user' || m.type === 'assistant')
        .map(m => ({
          role: m.type === 'user' ? 'user' : 'assistant',
          content: m.content
        }));
      
      // 启动对话流
      const request: KnowledgeGraphChatRequest = {
        question,
        collection_name: state.collectionName,
        session_id: state.currentSessionId,
        user_id: state.userId,
        chat_history: chatHistory.slice(0, -1), // 排除刚添加的当前问题
        files: fileUpload.uploadedFilePathsArray.value.length > 0 ? fileUpload.uploadedFilePathsArray.value : undefined // 添加已上传的文件路径数组
      };
      
      const response = await startKnowledgeGraphChatStream(request);
      
      if (response.status === 'success') {
        // 发送成功后清空文件列表
        if (fileUpload.uploadedFilePathsArray.value.length > 0) {
          fileUpload.clearAllFiles();
        }
        // 更新会话ID（如果后端返回了新的会话ID）
        if (response.session_id !== state.currentSessionId) {
          state.currentSessionId = response.session_id;
          // 重新建立SSE连接使用新的会话ID
          await establishSSEConnection(response.session_id);
        }
        
        state.isTyping = true;
        
        // 先初始化助手消息，带有初始状态
        const assistantMessage: ChatMessage = {
          id: `assistant_${Date.now()}`,
          type: 'assistant',
          content: t('status.processing'),
          timestamp: Date.now(),
          sources: [],
          images: [],
          isStatus: true // 标记为状态消息
        };
        
        messages.value.push(assistantMessage);
        
        // 滚动到底部，显示新的消息
        await nextTick();
        scrollToBottom();
        
        // 如果SSE连接断开了，重新建立连接
        if (!state.isConnected) {
          console.log('SSE连接已断开，重新建立连接...');
          await establishSSEConnection(state.currentSessionId);
        }
        
      } else {
        message.error(t('errors.startChatFailed'));
        state.isTyping = false;
      }

    } catch (error) {
      console.error('发送消息失败:', error);
      message.error(t('errors.sendMessageFailed'));
      state.isTyping = false;
    }
  };
  
  // 建立SSE连接
  const establishSSEConnection = async (sessionId: string) => {
    try {
      closeSSEConnection();
      
      // console.log('正在建立SSE连接，sessionId:', sessionId, 'userId:', state.userId);
      eventSource = createSSEConnection(sessionId, state.userId);
      
      eventSource.onopen = () => {
        state.isConnected = true;
        // console.log('SSE连接已建立');
      };
      
      eventSource.onmessage = (event) => {
        handleSSEMessage(event);
      };
      
      eventSource.onerror = (error: Event) => {
        console.error('SSE连接发生错误:', error);
        
        // 检查EventSource的状态
        if (eventSource) {
          console.error('EventSource 状态:', {
            readyState: eventSource.readyState,
            url: eventSource.url
          });
          
          // 根据readyState显示不同的错误信息
          switch (eventSource.readyState) {
            case EventSource.CONNECTING:
              console.log('正在尝试连接...');
              break;
            case EventSource.OPEN:
              console.log('连接已打开');
              break;
            case EventSource.CLOSED:
              console.error('连接已关闭');
              message.error(t('errors.sseConnectionError'));
              break;
          }
        }
        
        state.isConnected = false;
        state.isTyping = false;
      };
      
    } catch (error) {
      console.error('建立SSE连接失败:', error);
      message.error(t('errors.sseConnectionFailed'));
      state.isConnected = false;
      state.isTyping = false;
    }
  };
  
  // 处理SSE消息
  const handleSSEMessage = (event: MessageEvent) => {
    try {
      // 根据SSE的event type处理不同消息
      if (event.type === 'message') {
        // 解析消息数据
        const data: SSEMessage = JSON.parse(event.data);
        
        // console.log('SSE消息 [' + data.type + ']:', data.content || '无内容');
        
        switch (data.type) {
          case 'message':
            // 连接建立或一般消息
            if (data.content.includes('连接已建立')) {
              message.success(t('success.connectionEstablished'));
              // 如果有等待中的助手消息，更新其状态
              const lastMessage = messages.value[messages.value.length - 1];
              if (lastMessage && lastMessage.type === 'assistant' && lastMessage.content.includes('正在处理')) {
                updateAssistantStatus(t('status.connecting'));
              }
            }
            break;
            
          case 'status':
            // 状态消息，显示在UI上
            // console.log('📊 状态消息:', data.content);
            updateAssistantStatus(data.content);
            break;
            
          case 'sources_retrieved':
            // 检索到来源
            // console.log('📚 检索到来源数量:', data.source_count);
            const retrievedMsg = t('sse.retrievedSources', [data.source_count]);
            updateAssistantStatus(retrievedMsg);
            break;
            
          case 'token':
            // 流式输出token
            // console.log('🔤 Token:', data.content);
            appendToLastMessage(data.content);
            break;
            
          case 'sources':
            // 数据来源
            // console.log('📖 收到数据来源:', data.sources?.length || 0, '个');
            addSourcesToLastMessage(data.sources || []);
            break;
            
          case 'complete':
            // 完成
            state.isTyping = false;
            // console.log('✅ 对话完成');
            // 对话完成后重新加载会话列表，以获取最新的会话信息
            loadSessions();
            // 确保在对话完成后滚动到底部
            nextTick(() => {
              scheduleSmootScroll();
            });
            break;
            
          case 'error':
            // 错误
            state.isTyping = false;
            console.error('❌ 错误:', data.content);
            message.error(data.content);
            break;
            
          case 'cancelled':
            // 取消
            state.isTyping = false;
            // console.log('🚫 对话已取消');
            updateAssistantStatus(t('status.cancelled'));
            break;
            
          default:
            console.log('未知消息类型:', data.type);
        }
      } else if (event.type === 'heartbeat') {
        // 心跳消息，保持连接
        // 不需要输出日志
      } else {
        console.log('未知SSE事件类型:', event.type);
      }
      
    } catch (error) {
      console.error('处理SSE消息失败:', error, '原始数据:', event.data);
    }
  };
  
  // 检查是否是状态消息
  const isStatusMessage = (message: ChatMessage): boolean => {
    return message.isStatus === true;
  };
  
  // 更新助手消息的状态
  const updateAssistantStatus = (content: string) => {
    if (!content) return;
    
    // console.log('📊 更新助手状态:', content);
    
    // 找到最后一个助手消息
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.type === 'assistant') {
      // 如果是状态消息，则更新状态
      if (isStatusMessage(lastMessage)) {
        lastMessage.content = content;
        lastMessage.timestamp = Date.now();
        lastMessage.isStatus = true; // 标记为状态消息
        
        // 滚动到底部
        nextTick(() => {
          scheduleSmootScroll();
        });
      } else {
        // 如果不是状态消息，创建新的状态消息
        const statusMessage: ChatMessage = {
          id: `assistant_${Date.now()}`,
          type: 'assistant',
          content: content,
          timestamp: Date.now(),
          sources: [],
          images: [],
          isStatus: true
        };
        
        messages.value.push(statusMessage);
        
        // 滚动到底部
        nextTick(() => {
          scheduleSmootScroll();
        });
      }
    } else {
      // 没有助手消息，创建新的状态消息
      const statusMessage: ChatMessage = {
        id: `assistant_${Date.now()}`,
        type: 'assistant',
        content: content,
        timestamp: Date.now(),
        sources: [],
        images: [],
        isStatus: true
      };
      
      messages.value.push(statusMessage);
      
      // 滚动到底部
      nextTick(() => {
        scheduleSmootScroll();
      });
    }
  };
  
  // 添加内容到最后一条消息
  const appendToLastMessage = (content: string) => {
    if (!content) return;
    
    // console.log('📝 追加内容:', content.substring(0, 20) + (content.length > 20 ? '...' : ''));
    
    const lastMessage = messages.value[messages.value.length - 1];
    
    if (lastMessage && lastMessage.type === 'assistant') {
      // 如果这是第一个token，且当前内容是状态信息，则清空后再追加
      if (isStatusMessage(lastMessage)) {
        lastMessage.content = content; // 清空状态信息，设置为实际内容
        lastMessage.isStatus = false; // 清除状态消息标记
        // console.log('🔄 清空状态信息，开始流式输出');
      } else {
        // 否则正常追加内容
        lastMessage.content += content;
      }
      
      // 优化：减少频繁的数组重建，只在必要时触发更新
      // 通过修改消息的时间戳来触发响应式更新
      lastMessage.timestamp = Date.now();
      
      // 使用新的丝滑滚动调度器
      scheduleSmootScroll();
    } else {
      console.warn('⚠️ 没有找到助手消息，无法追加内容');
    }
  };
  
  // 丝滑滚动调度器
  let scrollAnimationId: number | null = null;
  let isScrolling = false;
  let lastScrollTime = 0;
  let pendingScroll = false;
  const SCROLL_THROTTLE_MS = 100; // 降低到100ms，减少频率但保持响应性
  
  const scheduleSmootScroll = () => {
    if (pendingScroll) return;
    
    // 智能检测：只有用户在底部附近时才自动滚动
    if (!shouldAutoScroll()) {
      return;
    }
    
    const now = performance.now();
    const timeSinceLastScroll = now - lastScrollTime;
    
    // 如果AI正在回复，减少节流时间以获得更好的实时滚动体验
    const throttleTime = state.isTyping ? 50 : SCROLL_THROTTLE_MS;
    
    if (timeSinceLastScroll >= throttleTime) {
      // 立即执行滚动
      pendingScroll = true;
      executeSmootScroll();
    } else {
      // 延迟执行
      pendingScroll = true;
      const delay = throttleTime - timeSinceLastScroll;
      setTimeout(() => {
        if (pendingScroll) {
          executeSmootScroll();
        }
      }, delay);
    }
  };
  
  // 检查是否应该自动滚动
  const shouldAutoScroll = (): boolean => {
    if (!messagesContainer.value) return false;
    
    const container = messagesContainer.value;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    
    // 如果用户距离底部超过100px，则不自动滚动
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    
    // 在AI正在回复时，即使用户不在底部也要自动滚动
    if (state.isTyping) {
      return true;
    }
    
    return distanceFromBottom <= 100;
  };
  
  const executeSmootScroll = () => {
    if (!messagesContainer.value || isScrolling) {
      pendingScroll = false;
      return;
    }
    
    const container = messagesContainer.value;
    const targetScrollTop = container.scrollHeight - container.clientHeight;
    const currentScrollTop = container.scrollTop;
    
    // 如果已经在底部附近，不需要滚动
    if (Math.abs(targetScrollTop - currentScrollTop) < 5) {
      pendingScroll = false;
      lastScrollTime = performance.now();
      return;
    }
    
    // 开始平滑滚动动画
    isScrolling = true;
    pendingScroll = false;
    
    const startTime = performance.now();
    const startScrollTop = currentScrollTop;
    const distance = targetScrollTop - startScrollTop;
    const duration = Math.min(300, Math.abs(distance) * 0.5); // 动态调整持续时间
    
    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数 (easeOutCubic) 让滚动更自然
      const easeProgress = 1 - Math.pow(1 - progress, 3);
      const newScrollTop = startScrollTop + (distance * easeProgress);
      
      container.scrollTop = newScrollTop;
      
      if (progress < 1) {
        scrollAnimationId = requestAnimationFrame(animateScroll);
      } else {
        // 动画完成
        isScrolling = false;
        lastScrollTime = performance.now();
        scrollAnimationId = null;
        
        // 确保最终位置准确
        container.scrollTop = targetScrollTop;
      }
    };
    
    scrollAnimationId = requestAnimationFrame(animateScroll);
  };
  
  // 立即滚动到底部（不使用动画）
  const scrollToBottom = () => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  };
  
  // 滚动节流定时器
  let scrollTimeout: number | null = null;
  
  // 添加来源到最后一条消息
  const addSourcesToLastMessage = (sources: SourceInfo[]) => {
    if (!sources || sources.length === 0) return;
    
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.type === 'assistant') {
      // console.log('📚 添加来源信息:', sources.length, '个');
      
      lastMessage.sources = sources;
      
      // 分离图片和文本来源
      const images: ImageInfo[] = [];
      sources.forEach(source => {
        if (source.type === 'image' && source.url) {
          images.push({
            url: source.url,
            name: source.name,
            alt: source.name
          });
        }
      });
      
      lastMessage.images = images;
      
      // 触发响应式更新
      lastMessage.timestamp = Date.now();
      
      // 滚动到底部，显示来源信息
      nextTick(() => {
        scheduleSmootScroll();
      });
    }
  };
  
  // 结束当前会话
  const endCurrentSession = async () => {
    if (!state.currentSessionId) {
      return; // 没有当前会话，无需结束
    }
    
    try {
      // 检查当前会话是否有实际的对话内容
      const hasMessages = messages.value.length > 0;
      const hasUserMessages = messages.value.some(m => m.type === 'user');
      
      if (!hasMessages || !hasUserMessages) {
        // 如果没有消息或没有用户消息，说明是新建的空会话，无需调用后端接口
        // console.log('当前会话无对话内容，跳过结束会话操作:', state.currentSessionId);
      } else {
        // console.log('正在结束当前会话:', state.currentSessionId);
        
        // 调用后端接口结束会话
        const response = await endChatSession(state.currentSessionId);
        
        if (response.code === 200) {
          // console.log('会话结束成功:', state.currentSessionId);
        } else {
          console.warn('结束会话响应异常，但继续执行:', response.message);
        }
      }
      
    } catch (error: any) {
      // 捕获所有错误，但不影响用户体验
      console.warn('结束当前会话状态失败，但继续执行:', error?.message || error);
      
      // 如果是404错误（会话不存在）或其他预期错误，不显示错误提示
      if (error?.response?.status === 404 || error?.response?.status === 500) {
        console.log('会话可能不存在于数据库中，这是正常情况（新建未使用的会话）');
      }
    }
    
    // 关闭SSE连接
    closeSSEConnection();
    
    // 重置状态
    state.isTyping = false;
    state.isConnected = false;
  };

  // 关闭SSE连接
  const closeSSEConnection = () => {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
      state.isConnected = false;
    }
  };
  
  // 取消当前会话
  const cancelCurrentSession = async () => {
    if (state.currentSessionId) {
      try {
        await cancelRagSession(state.currentSessionId);
        await endCurrentSession(); // 取消会话时也要设置状态为结束
        message.success(t('success.sessionCancelled'));
      } catch (error) {
        console.error('取消会话失败:', error);
        message.error(t('errors.cancelSessionFailed'));
      }
    }
  };
  
  // 清空消息
  const clearMessages = () => {
    messages.value = [];
    console.log(t('log.messagesCleared'));
  };
  
  // 处理键盘事件
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };
  
  // 处理路径链接点击
  const handlePathLinkClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.tagName === 'A' && target.textContent) {
      // 这里可以添加处理文件路径的逻辑
      console.log('点击路径链接:', target.textContent);
    }
  };
  
  // 监听滚动事件，控制滚动到底部按钮的显示
  const handleScroll = () => {
    if (!messagesContainer.value) return;
    
    const container = messagesContainer.value;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    
    // 如果用户距离底部超过200px，显示滚动到底部按钮
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    showScrollToBottomButton.value = distanceFromBottom > 200;
  };
  
  // 强制滚动到底部（带动画）
  const forceScrollToBottom = () => {
    pendingScroll = true;
    executeSmootScroll();
  };
  
  // 组件卸载时清理
  onUnmounted(async () => {
    // 结束当前会话
    await endCurrentSession();
    
    // 清理所有定时器和动画
    if (scrollTimeout) {
      window.clearTimeout(scrollTimeout);
      scrollTimeout = null;
    }
    
    if (scrollAnimationId) {
      cancelAnimationFrame(scrollAnimationId);
      scrollAnimationId = null;
    }
    
    // 重置滚动状态
    isScrolling = false;
    pendingScroll = false;
    
    console.log(t('log.componentUnmounted'));
  });
  
  return {
    // 状态
    state,
    messages,
    inputMessage,
    collections,
    sessions,
    currentSession,
    canSend,
    statusText,
    
    // 分页相关
    pagination,
    handlePageChange,
    
    // DOM引用
    messagesContainer,
    messageInput,
    
    // 方法
    initialize,
    loadCollections,
    loadSessions,
    createNewSession,
    switchSession,
    deleteSession,
    renameSession,
    exportSession,
    sendMessage,
    cancelCurrentSession,
    clearMessages,
    scrollToBottom,
    handleKeyDown,
    handlePathLinkClick,
    
    // 工具方法
    renderMarkdown,
    formatTime,
    formatDate,
    
    // 滚动到底部按钮的显示状态
    showScrollToBottomButton,
    handleScroll,
    forceScrollToBottom,
    
    // 文件上传相关
    fileUpload
  };
} 
